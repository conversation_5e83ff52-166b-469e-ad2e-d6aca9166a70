<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 1675.00390625 3533.15625" style="max-width: 1675.00390625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d"><style>#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .error-icon{fill:#a44141;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .error-text{fill:#ddd;stroke:#ddd;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .edge-thickness-normal{stroke-width:1px;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .edge-thickness-thick{stroke-width:3.5px;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .edge-pattern-solid{stroke-dasharray:0;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .marker.cross{stroke:lightgrey;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d p{margin:0;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .cluster-label text{fill:#F9FFFE;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .cluster-label span{color:#F9FFFE;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .cluster-label span p{background-color:transparent;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .label text,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d span{fill:#ccc;color:#ccc;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .node rect,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .node circle,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .node ellipse,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .node polygon,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .rough-node .label text,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .node .label text,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .image-shape .label,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .icon-shape .label{text-anchor:middle;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .rough-node .label,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .node .label,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .image-shape .label,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .icon-shape .label{text-align:center;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .node.clickable{cursor:pointer;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .arrowheadPath{fill:lightgrey;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .cluster text{fill:#F9FFFE;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .cluster span{color:#F9FFFE;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d rect.text{fill:none;stroke-width:0;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .icon-shape,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .icon-shape p,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .icon-shape rect,#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .startEnd&gt;*{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:3px!important;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .startEnd span{fill:#e8f5e8!important;stroke:#2e7d32!important;stroke-width:3px!important;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .process&gt;*{fill:#e3f2fd!important;stroke:#1565c0!important;stroke-width:2px!important;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .process span{fill:#e3f2fd!important;stroke:#1565c0!important;stroke-width:2px!important;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .decision&gt;*{fill:#fff3e0!important;stroke:#ef6c00!important;stroke-width:2px!important;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .decision span{fill:#fff3e0!important;stroke:#ef6c00!important;stroke-width:2px!important;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .important&gt;*{fill:#fce4ec!important;stroke:#c2185b!important;stroke-width:2px!important;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .important span{fill:#fce4ec!important;stroke:#c2185b!important;stroke-width:2px!important;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .reinsurance&gt;*{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;}#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d .reinsurance span{fill:#f3e5f5!important;stroke:#7b1fa2!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"></g><g class="edgePaths"><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Start_Opportunity_0" d="M891.16,47L878.764,51.167C866.367,55.333,841.574,63.667,829.178,89.089C816.781,114.51,816.781,157.021,816.781,201.531C816.781,246.042,816.781,292.552,816.781,326.474C816.781,360.396,816.781,381.729,816.781,401.063C816.781,420.396,816.781,437.729,826.124,450.305C835.467,462.881,854.153,470.7,863.496,474.609L872.839,478.518"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Opportunity_Discovery_1" d="M969.734,558.063L969.734,562.229C969.734,566.396,969.734,574.729,969.805,582.479C969.875,590.229,970.015,597.396,970.086,600.98L970.156,604.563"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Discovery_PricingReq_2" d="M941.886,749.73L936.33,760.538C930.773,771.346,919.66,792.962,914.103,809.27C908.547,825.578,908.547,836.578,908.547,842.078L908.547,847.578"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PricingReq_Quote_3" d="M908.547,905.578L908.547,911.745C908.547,917.911,908.547,930.245,908.547,941.911C908.547,953.578,908.547,964.578,908.547,970.078L908.547,975.578"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Quote_ClientReview_4" d="M908.547,1033.578L908.547,1037.745C908.547,1041.911,908.547,1050.245,913.704,1062.869C918.861,1075.494,929.175,1092.41,934.332,1100.868L939.489,1109.326"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClientReview_Confirmed_5" d="M1011.313,1194.905L1026.559,1207.835C1041.804,1220.765,1072.295,1246.625,1087.54,1265.055C1102.785,1283.484,1102.785,1294.484,1102.785,1299.984L1102.785,1305.484"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClientReview_Lost_6" d="M930.955,1196.705L917.256,1209.335C903.556,1221.965,876.157,1247.225,862.457,1270.521C848.758,1293.818,848.758,1315.151,848.758,1334.484C848.758,1353.818,848.758,1371.151,848.758,1388.484C848.758,1405.818,848.758,1423.151,848.758,1440.484C848.758,1457.818,848.758,1475.151,848.758,1492.484C848.758,1509.818,848.758,1527.151,848.758,1544.484C848.758,1561.818,848.758,1579.151,848.758,1609.055C848.758,1638.958,848.758,1681.432,848.758,1725.906C848.758,1770.38,848.758,1816.854,848.758,1852.758C848.758,1888.661,848.758,1913.995,848.758,1939.328C848.758,1964.661,848.758,1989.995,848.758,2013.328C848.758,2036.661,848.758,2057.995,848.758,2077.328C848.758,2096.661,848.758,2113.995,848.758,2131.328C848.758,2148.661,848.758,2165.995,848.758,2183.328C848.758,2200.661,848.758,2217.995,848.758,2235.328C848.758,2252.661,848.758,2269.995,848.758,2287.328C848.758,2304.661,848.758,2321.995,848.758,2346.949C848.758,2371.904,848.758,2404.479,848.758,2439.055C848.758,2473.63,848.758,2510.206,848.758,2547.952C848.758,2585.698,848.758,2624.615,848.758,2663.531C848.758,2702.448,848.758,2741.365,848.758,2766.323C848.758,2791.281,848.758,2802.281,848.758,2807.781L848.758,2813.281"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClientReview_Discovery_7" d="M998.897,1112.741L1004.235,1103.714C1009.572,1094.687,1020.247,1076.632,1025.584,1058.939C1030.922,1041.245,1030.922,1023.911,1030.922,1004.578C1030.922,985.245,1030.922,963.911,1030.922,942.578C1030.922,921.245,1030.922,899.911,1030.922,878.578C1030.922,857.245,1030.922,835.911,1025.829,815.033C1020.737,794.155,1010.552,773.733,1005.46,763.521L1000.367,753.31"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Confirmed_CreatePolicy_8" d="M1102.785,1363.484L1102.785,1367.651C1102.785,1371.818,1102.785,1380.151,1102.785,1387.818C1102.785,1395.484,1102.785,1402.484,1102.785,1405.984L1102.785,1409.484"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CreatePolicy_OnCover_9" d="M1071.378,1467.484L1066.531,1471.651C1061.684,1475.818,1051.991,1484.151,1047.144,1491.818C1042.297,1499.484,1042.297,1506.484,1042.297,1509.984L1042.297,1513.484"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OnCover_InsureObjects_10" d="M956.32,1550.154L839.227,1557.876C722.134,1565.598,487.948,1581.041,370.931,1595.825C253.914,1610.609,254.066,1624.735,254.142,1631.797L254.219,1638.86"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_InsureObjects_Vehicle_11" d="M213.914,1765.605L197.798,1781.892C181.682,1798.179,149.45,1830.754,133.335,1854.541C117.219,1878.328,117.219,1893.328,117.219,1900.828L117.219,1908.328"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_InsureObjects_Property_12" d="M294.61,1765.605L310.559,1781.892C326.508,1798.179,358.406,1830.754,374.356,1854.541C390.305,1878.328,390.305,1893.328,390.305,1900.828L390.305,1908.328"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_InsureObjects_InsuredObject_13" d="M315.102,1745.113L373.153,1764.816C431.203,1784.518,547.305,1823.923,605.356,1851.126C663.406,1878.328,663.406,1893.328,663.406,1900.828L663.406,1908.328"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Vehicle_Covers_14" d="M117.219,1966.328L117.219,1974.495C117.219,1982.661,117.219,1998.995,142.883,2013.176C168.547,2027.357,219.874,2039.386,245.538,2045.401L271.202,2051.415"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Property_Covers_15" d="M390.305,1966.328L390.305,1974.495C390.305,1982.661,390.305,1998.995,390.305,2012.661C390.305,2026.328,390.305,2037.328,390.305,2042.828L390.305,2048.328"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_InsuredObject_Covers_16" d="M663.406,1966.328L663.406,1974.495C663.406,1982.661,663.406,1998.995,637.741,2013.176C612.075,2027.357,560.745,2039.386,535.079,2045.401L509.414,2051.415"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Covers_Premium_17" d="M390.305,2106.328L390.305,2110.495C390.305,2114.661,390.305,2122.995,390.305,2130.661C390.305,2138.328,390.305,2145.328,390.305,2148.828L390.305,2152.328"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Premium_Receipt_18" d="M390.305,2210.328L390.305,2214.495C390.305,2218.661,390.305,2226.995,390.305,2234.661C390.305,2242.328,390.305,2249.328,390.305,2252.828L390.305,2256.328"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Receipt_Payment_19" d="M390.305,2314.328L390.305,2318.495C390.305,2322.661,390.305,2330.995,390.305,2346.283C390.305,2361.57,390.305,2383.813,390.305,2394.934L390.305,2406.055"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Payment_Allocation_20" d="M390.305,2464.055L390.305,2477.842C390.305,2491.63,390.305,2519.206,390.305,2547.285C390.305,2575.365,390.305,2603.948,390.305,2618.24L390.305,2632.531"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OnCover_Reinsurance_21" d="M1128.273,1556.332L1176.838,1563.024C1225.402,1569.716,1322.531,1583.1,1371.166,1593.376C1419.801,1603.651,1419.941,1610.818,1420.011,1614.402L1420.082,1617.985"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Reinsurance_ReinsOpp_22" d="M1466.968,1780.021L1478.64,1793.905C1490.313,1807.79,1513.659,1835.559,1525.331,1854.944C1537.004,1874.328,1537.004,1885.328,1537.004,1890.828L1537.004,1896.328"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Reinsurance_ActivePolicy_23" d="M1363.625,1770.293L1344.334,1785.799C1325.044,1801.305,1286.463,1832.316,1267.173,1860.489C1247.883,1888.661,1247.883,1913.995,1247.883,1939.328C1247.883,1964.661,1247.883,1989.995,1247.883,2013.328C1247.883,2036.661,1247.883,2057.995,1247.883,2077.328C1247.883,2096.661,1247.883,2113.995,1247.883,2131.328C1247.883,2148.661,1247.883,2165.995,1247.883,2183.328C1247.883,2200.661,1247.883,2217.995,1263.249,2231.313C1278.616,2244.632,1309.349,2253.935,1324.715,2258.587L1340.082,2263.238"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReinsOpp_ReinsContract_24" d="M1537.004,1978.328L1537.004,1984.495C1537.004,1990.661,1537.004,2002.995,1537.004,2014.661C1537.004,2026.328,1537.004,2037.328,1537.004,2042.828L1537.004,2048.328"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReinsContract_ReinsSection_25" d="M1537.004,2106.328L1537.004,2110.495C1537.004,2114.661,1537.004,2122.995,1537.004,2130.661C1537.004,2138.328,1537.004,2145.328,1537.004,2148.828L1537.004,2152.328"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReinsSection_ActivePolicy_26" d="M1537.004,2210.328L1537.004,2214.495C1537.004,2218.661,1537.004,2226.995,1528.211,2235.058C1519.418,2243.121,1501.832,2250.914,1493.039,2254.811L1484.246,2258.708"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ActivePolicy_ClaimEvent_27" d="M1343.91,2292.228L1222.55,2300.078C1101.19,2307.928,858.47,2323.628,737.18,2335.062C615.891,2346.495,616.031,2353.662,616.101,2357.245L616.172,2360.829"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClaimEvent_CreateClaim_28" d="M616.25,2510.281L616.167,2516.365C616.083,2522.448,615.917,2534.615,615.833,2554.99C615.75,2575.365,615.75,2603.948,615.75,2618.24L615.75,2632.531"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClaimEvent_Renewal_29" d="M672.941,2453.59L728.061,2469.122C783.182,2484.654,893.423,2515.718,948.618,2536.833C1003.813,2557.948,1003.962,2569.115,1004.036,2574.698L1004.111,2580.282"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CreateClaim_ClaimAssess_30" d="M615.75,2690.531L615.75,2705.49C615.75,2720.448,615.75,2750.365,615.75,2770.823C615.75,2791.281,615.75,2802.281,615.75,2807.781L615.75,2813.281"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClaimAssess_ClaimApproval_31" d="M615.75,2871.281L615.75,2875.448C615.75,2879.615,615.75,2887.948,615.82,2895.698C615.891,2903.448,616.031,2910.615,616.101,2914.199L616.172,2917.782"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClaimApproval_ClaimPayout_32" d="M595.35,3064.756L591.962,3074.323C588.574,3083.889,581.799,3103.023,578.411,3118.09C575.023,3133.156,575.023,3144.156,575.023,3149.656L575.023,3155.156"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClaimApproval_ClaimReject_33" d="M676.698,3025.209L722.298,3041.367C767.899,3057.525,859.1,3089.84,962.066,3114.658C1065.033,3139.476,1179.766,3156.796,1237.132,3165.456L1294.498,3174.116"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClaimPayout_ClaimReceipt_34" d="M575.023,3213.156L575.023,3217.323C575.023,3221.49,575.023,3229.823,575.023,3237.49C575.023,3245.156,575.023,3252.156,575.023,3255.656L575.023,3259.156"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClaimReceipt_ReinsRecovery_35" d="M575.023,3317.156L575.023,3321.323C575.023,3325.49,575.023,3333.823,575.023,3341.49C575.023,3349.156,575.023,3356.156,575.023,3359.656L575.023,3363.156"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ReinsRecovery_ClaimSettled_36" d="M575.023,3421.156L575.023,3425.323C575.023,3429.49,575.023,3437.823,644.546,3449.227C714.068,3460.631,853.113,3475.105,922.636,3482.342L992.158,3489.579"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Renewal_Amendment_37" d="M1004.164,2743.781L1004.081,2749.865C1003.997,2755.948,1003.831,2768.115,1011.326,2779.961C1018.821,2791.807,1033.978,2803.334,1041.557,2809.097L1049.135,2814.86"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Renewal_Expiry_38" d="M1061.867,2686.078L1103.096,2701.778C1144.324,2717.479,1226.781,2748.88,1268.01,2770.081C1309.238,2791.281,1309.238,2802.281,1309.238,2807.781L1309.238,2813.281"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Amendment_CreatePolicy_39" d="M1119.654,2817.281L1126.924,2811.115C1134.194,2804.948,1148.734,2792.615,1156.004,2766.99C1163.273,2741.365,1163.273,2702.448,1163.273,2663.531C1163.273,2624.615,1163.273,2585.698,1163.273,2547.952C1163.273,2510.206,1163.273,2473.63,1163.273,2439.055C1163.273,2404.479,1163.273,2371.904,1163.273,2346.949C1163.273,2321.995,1163.273,2304.661,1163.273,2287.328C1163.273,2269.995,1163.273,2252.661,1163.273,2235.328C1163.273,2217.995,1163.273,2200.661,1163.273,2183.328C1163.273,2165.995,1163.273,2148.661,1163.273,2131.328C1163.273,2113.995,1163.273,2096.661,1163.273,2077.328C1163.273,2057.995,1163.273,2036.661,1163.273,2013.328C1163.273,1989.995,1163.273,1964.661,1163.273,1939.328C1163.273,1913.995,1163.273,1888.661,1163.273,1852.758C1163.273,1816.854,1163.273,1770.38,1163.273,1725.906C1163.273,1681.432,1163.273,1638.958,1163.273,1609.055C1163.273,1579.151,1163.273,1561.818,1163.273,1544.484C1163.273,1527.151,1163.273,1509.818,1158.932,1497.419C1154.591,1485.02,1145.908,1477.556,1141.567,1473.824L1137.226,1470.092"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Start_Campaign_40" d="M979.309,47L985.748,51.167C992.186,55.333,1005.064,63.667,1011.573,71.417C1018.082,79.167,1018.222,86.334,1018.293,89.917L1018.363,93.501"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Campaign_CampaignLaunch_41" d="M984.665,268.786L978.786,280.499C972.908,292.211,961.151,315.637,955.273,332.85C949.395,350.063,949.395,361.063,949.395,366.563L949.395,372.063"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CampaignLaunch_Opportunity_42" d="M949.395,430.063L949.395,434.229C949.395,438.396,949.395,446.729,950.517,454.427C951.639,462.125,953.884,469.188,955.006,472.719L956.128,476.25"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Campaign_Opportunity_43" d="M1052.218,268.786L1057.93,280.499C1063.642,292.211,1075.065,315.637,1080.777,338.016C1086.488,360.396,1086.488,381.729,1086.488,401.063C1086.488,420.396,1086.488,437.729,1079.472,450.242C1072.455,462.755,1058.422,470.447,1051.405,474.294L1044.389,478.14"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClaimSettled_ActivePolicy_44" d="M1152.965,3488.223L1208.314,3481.212C1263.664,3474.201,1374.363,3460.179,1429.713,3444.501C1485.063,3428.823,1485.063,3411.49,1485.063,3394.156C1485.063,3376.823,1485.063,3359.49,1485.063,3342.156C1485.063,3324.823,1485.063,3307.49,1485.063,3290.156C1485.063,3272.823,1485.063,3255.49,1485.063,3238.156C1485.063,3220.823,1485.063,3203.49,1485.063,3184.156C1485.063,3164.823,1485.063,3143.49,1485.063,3113C1485.063,3082.51,1485.063,3042.865,1485.063,3005.219C1485.063,2967.573,1485.063,2931.927,1485.063,2905.438C1485.063,2878.948,1485.063,2861.615,1485.063,2842.281C1485.063,2822.948,1485.063,2801.615,1485.063,2771.49C1485.063,2741.365,1485.063,2702.448,1485.063,2663.531C1485.063,2624.615,1485.063,2585.698,1485.063,2547.952C1485.063,2510.206,1485.063,2473.63,1485.063,2439.055C1485.063,2404.479,1485.063,2371.904,1480.344,2351.864C1475.625,2331.825,1466.188,2324.321,1461.469,2320.569L1456.75,2316.817"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ClaimReject_ActivePolicy_45" d="M1393.412,3159.156L1397.787,3152.99C1402.161,3146.823,1410.911,3134.49,1415.285,3108.5C1419.66,3082.51,1419.66,3042.865,1419.66,3005.219C1419.66,2967.573,1419.66,2931.927,1419.66,2905.438C1419.66,2878.948,1419.66,2861.615,1419.66,2842.281C1419.66,2822.948,1419.66,2801.615,1419.66,2771.49C1419.66,2741.365,1419.66,2702.448,1419.66,2663.531C1419.66,2624.615,1419.66,2585.698,1419.66,2547.952C1419.66,2510.206,1419.66,2473.63,1419.66,2439.055C1419.66,2404.479,1419.66,2371.904,1419.66,2352.116C1419.66,2332.328,1419.66,2325.328,1419.66,2321.828L1419.66,2318.328"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Lost_End_46" d="M848.758,2871.281L848.758,2875.448C848.758,2879.615,848.758,2887.948,882.518,2907.697C916.278,2927.446,983.798,2958.611,1017.557,2974.194L1051.317,2989.776"></path><path marker-end="url(#mermaid-3c492ff0-0596-411b-89a3-b26d38485c2d_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Expiry_End_47" d="M1309.238,2871.281L1309.238,2875.448C1309.238,2879.615,1309.238,2887.948,1275.958,2907.669C1242.678,2927.391,1176.118,2958.501,1142.837,2974.055L1109.557,2989.61"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(908.546875, 814.578125)" class="edgeLabel"><g transform="translate(-40.890625, -12)" class="label"><foreignObject height="24" width="81.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Gather Info</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1102.78515625, 1272.484375)" class="edgeLabel"><g transform="translate(-24.6328125, -12)" class="label"><foreignObject height="24" width="49.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Accept</p></span></div></foreignObject></g></g><g transform="translate(848.7578125, 2015.328125)" class="edgeLabel"><g transform="translate(-23.125, -12)" class="label"><foreignObject height="24" width="46.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Reject</p></span></div></foreignObject></g></g><g transform="translate(1030.921875, 942.578125)" class="edgeLabel"><g transform="translate(-23.609375, -12)" class="label"><foreignObject height="24" width="47.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Modify</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(117.21875, 1863.328125)" class="edgeLabel"><g transform="translate(-25.8828125, -12)" class="label"><foreignObject height="24" width="51.765625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Vehicle</p></span></div></foreignObject></g></g><g transform="translate(390.3046875, 1863.328125)" class="edgeLabel"><g transform="translate(-30.5390625, -12)" class="label"><foreignObject height="24" width="61.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Property</p></span></div></foreignObject></g></g><g transform="translate(663.40625, 1863.328125)" class="edgeLabel"><g transform="translate(-20.40625, -12)" class="label"><foreignObject height="24" width="40.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Other</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1537.00390625, 1863.328125)" class="edgeLabel"><g transform="translate(-11.328125, -12)" class="label"><foreignObject height="24" width="22.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g transform="translate(1247.8828125, 2079.328125)" class="edgeLabel"><g transform="translate(-9.3984375, -12)" class="label"><foreignObject height="24" width="18.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(615.75, 2546.78125)" class="edgeLabel"><g transform="translate(-11.328125, -12)" class="label"><foreignObject height="24" width="22.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g transform="translate(1003.6640625, 2546.78125)" class="edgeLabel"><g transform="translate(-9.3984375, -12)" class="label"><foreignObject height="24" width="18.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(575.0234375, 3122.15625)" class="edgeLabel"><g transform="translate(-11.328125, -12)" class="label"><foreignObject height="24" width="22.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g transform="translate(950.30078125, 3122.15625)" class="edgeLabel"><g transform="translate(-9.3984375, -12)" class="label"><foreignObject height="24" width="18.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1003.6640625, 2780.28125)" class="edgeLabel"><g transform="translate(-11.328125, -12)" class="label"><foreignObject height="24" width="22.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g transform="translate(1309.23828125, 2780.28125)" class="edgeLabel"><g transform="translate(-9.3984375, -12)" class="label"><foreignObject height="24" width="18.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(949.39453125, 339.0625)" class="edgeLabel"><g transform="translate(-11.328125, -12)" class="label"><foreignObject height="24" width="22.65625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Yes</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1086.48828125, 403.0625)" class="edgeLabel"><g transform="translate(-9.3984375, -12)" class="label"><foreignObject height="24" width="18.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>No</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(949.17578125, 27.5)" id="flowchart-Start-180" class="node default startEnd"><rect height="39" width="147.046875" y="-19.5" x="-73.5234375" ry="19.5" rx="19.5" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-61.1484375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="122.296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Customer Inquiry</p></span></div></foreignObject></g></g><g transform="translate(969.734375, 519.0625)" id="flowchart-Opportunity-181" class="node default process"><rect height="78" width="260" y="-39" x="-130" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Insurance Opportunity</p></span></div></foreignObject></g></g><g transform="translate(969.734375, 692.8203125)" id="flowchart-Discovery-183" class="node default decision"><polygon style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" transform="translate(-84.7578125,84.7578125)" class="label-container" points="84.7578125,0 169.515625,-84.7578125 84.7578125,-169.515625 0,-84.7578125"></polygon><g transform="translate(-57.7578125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="115.515625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Client Discovery</p></span></div></foreignObject></g></g><g transform="translate(908.546875, 878.578125)" id="flowchart-PricingReq-185" class="node default"><rect height="54" width="169.625" y="-27" x="-84.8125" style="" class="basic label-container"></rect><g transform="translate(-54.8125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="109.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Request Pricing</p></span></div></foreignObject></g></g><g transform="translate(908.546875, 1006.578125)" id="flowchart-Quote-187" class="node default"><rect height="54" width="174.75" y="-27" x="-87.375" style="" class="basic label-container"></rect><g transform="translate(-57.375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="114.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Generate Quote</p></span></div></foreignObject></g></g><g transform="translate(969.734375, 1159.53125)" id="flowchart-ClientReview-189" class="node default decision"><polygon style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" transform="translate(-75.953125,75.953125)" class="label-container" points="75.953125,0 151.90625,-75.953125 75.953125,-151.90625 0,-75.953125"></polygon><g transform="translate(-48.953125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="97.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Client Review</p></span></div></foreignObject></g></g><g transform="translate(1102.78515625, 1336.484375)" id="flowchart-Confirmed-191" class="node default important"><rect height="54" width="203.625" y="-27" x="-101.8125" style="fill:#fce4ec !important;stroke:#c2185b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-71.8125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="143.625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Confirmed by Client</p></span></div></foreignObject></g></g><g transform="translate(848.7578125, 2844.28125)" id="flowchart-Lost-193" class="node default"><rect height="54" width="180.375" y="-27" x="-90.1875" style="" class="basic label-container"></rect><g transform="translate(-60.1875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="120.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Lost Opportunity</p></span></div></foreignObject></g></g><g transform="translate(1102.78515625, 1440.484375)" id="flowchart-CreatePolicy-197" class="node default process"><rect height="54" width="241.140625" y="-27" x="-120.5703125" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-90.5703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="181.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Policy Transaction</p></span></div></foreignObject></g></g><g transform="translate(1042.296875, 1544.484375)" id="flowchart-OnCover-199" class="node default process"><rect height="54" width="171.953125" y="-27" x="-85.9765625" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-55.9765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="111.953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Policy On Cover</p></span></div></foreignObject></g></g><g transform="translate(253.76171875, 1723.90625)" id="flowchart-InsureObjects-201" class="node default decision"><polygon style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" transform="translate(-81.546875,81.546875)" class="label-container" points="81.546875,0 163.09375,-81.546875 81.546875,-163.09375 0,-81.546875"></polygon><g transform="translate(-54.546875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="109.09375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Insure Objects?</p></span></div></foreignObject></g></g><g transform="translate(117.21875, 1939.328125)" id="flowchart-Vehicle-203" class="node default process"><rect height="54" width="218.4375" y="-27" x="-109.21875" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-79.21875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="158.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Vehicle Record</p></span></div></foreignObject></g></g><g transform="translate(390.3046875, 1939.328125)" id="flowchart-Property-205" class="node default process"><rect height="54" width="227.734375" y="-27" x="-113.8671875" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-83.8671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="167.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Property Record</p></span></div></foreignObject></g></g><g transform="translate(663.40625, 1939.328125)" id="flowchart-InsuredObject-207" class="node default process"><rect height="54" width="218.46875" y="-27" x="-109.234375" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-79.234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="158.46875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Insured Object</p></span></div></foreignObject></g></g><g transform="translate(390.3046875, 2079.328125)" id="flowchart-Covers-209" class="node default process"><rect height="54" width="233.484375" y="-27" x="-116.7421875" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-86.7421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="173.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Insurance Covers</p></span></div></foreignObject></g></g><g transform="translate(390.3046875, 2183.328125)" id="flowchart-Premium-215" class="node default process"><rect height="54" width="195.359375" y="-27" x="-97.6796875" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-67.6796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="135.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Calculate Premium</p></span></div></foreignObject></g></g><g transform="translate(390.3046875, 2287.328125)" id="flowchart-Receipt-217" class="node default process"><rect height="54" width="185.390625" y="-27" x="-92.6953125" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-62.6953125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="125.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Generate Receipt</p></span></div></foreignObject></g></g><g transform="translate(390.3046875, 2437.0546875)" id="flowchart-Payment-219" class="node default process"><rect height="54" width="178.96875" y="-27" x="-89.484375" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-59.484375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="118.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Process Payment</p></span></div></foreignObject></g></g><g transform="translate(390.3046875, 2663.53125)" id="flowchart-Allocation-221" class="node default"><rect height="54" width="197.53125" y="-27" x="-98.765625" style="" class="basic label-container"></rect><g transform="translate(-68.765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="137.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Payment Allocation</p></span></div></foreignObject></g></g><g transform="translate(1419.66015625, 1723.90625)" id="flowchart-Reinsurance-223" class="node default decision"><polygon style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" transform="translate(-102.421875,102.421875)" class="label-container" points="102.421875,0 204.84375,-102.421875 102.421875,-204.84375 0,-102.421875"></polygon><g transform="translate(-75.421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="150.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Reinsurance Needed?</p></span></div></foreignObject></g></g><g transform="translate(1537.00390625, 1939.328125)" id="flowchart-ReinsOpp-225" class="node default reinsurance"><rect height="78" width="260" y="-39" x="-130" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Reinsurance Opportunity</p></span></div></foreignObject></g></g><g transform="translate(1419.66015625, 2287.328125)" id="flowchart-ActivePolicy-227" class="node default important"><rect height="54" width="151.5" y="-27" x="-75.75" style="fill:#fce4ec !important;stroke:#c2185b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-45.75, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="91.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Active Policy</p></span></div></foreignObject></g></g><g transform="translate(1537.00390625, 2079.328125)" id="flowchart-ReinsContract-229" class="node default reinsurance"><rect height="54" width="212.890625" y="-27" x="-106.4453125" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-76.4453125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="152.890625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Reinsurance Contract</p></span></div></foreignObject></g></g><g transform="translate(1537.00390625, 2183.328125)" id="flowchart-ReinsSection-231" class="node default reinsurance"><rect height="54" width="209.8125" y="-27" x="-104.90625" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-74.90625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="149.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Reinsurance Sections</p></span></div></foreignObject></g></g><g transform="translate(615.75, 2437.0546875)" id="flowchart-ClaimEvent-235" class="node default decision"><polygon style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" transform="translate(-72.7265625,72.7265625)" class="label-container" points="72.7265625,0 145.453125,-72.7265625 72.7265625,-145.453125 0,-72.7265625"></polygon><g transform="translate(-45.7265625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="91.453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Claim Event?</p></span></div></foreignObject></g></g><g transform="translate(615.75, 2663.53125)" id="flowchart-CreateClaim-237" class="node default process"><rect height="54" width="153.359375" y="-27" x="-76.6796875" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-46.6796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="93.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Create Claim</p></span></div></foreignObject></g></g><g transform="translate(1003.6640625, 2663.53125)" id="flowchart-Renewal-239" class="node default decision"><polygon style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" transform="translate(-79.75,79.75)" class="label-container" points="79.75,0 159.5,-79.75 79.75,-159.5 0,-79.75"></polygon><g transform="translate(-52.75, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="105.5"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Renewal Time?</p></span></div></foreignObject></g></g><g transform="translate(615.75, 2844.28125)" id="flowchart-ClaimAssess-241" class="node default"><rect height="54" width="185.640625" y="-27" x="-92.8203125" style="" class="basic label-container"></rect><g transform="translate(-62.8203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="125.640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Claim Assessment</p></span></div></foreignObject></g></g><g transform="translate(615.75, 3003.21875)" id="flowchart-ClaimApproval-243" class="node default decision"><polygon style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" transform="translate(-81.9375,81.9375)" class="label-container" points="81.9375,0 163.875,-81.9375 81.9375,-163.875 0,-81.9375"></polygon><g transform="translate(-54.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="109.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Approve Claim?</p></span></div></foreignObject></g></g><g transform="translate(575.0234375, 3186.15625)" id="flowchart-ClaimPayout-245" class="node default process"><rect height="54" width="165.546875" y="-27" x="-82.7734375" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-52.7734375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="105.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Process Payout</p></span></div></foreignObject></g></g><g transform="translate(1374.2578125, 3186.15625)" id="flowchart-ClaimReject-247" class="node default"><rect height="54" width="151.609375" y="-27" x="-75.8046875" style="" class="basic label-container"></rect><g transform="translate(-45.8046875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="91.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Reject Claim</p></span></div></foreignObject></g></g><g transform="translate(575.0234375, 3290.15625)" id="flowchart-ClaimReceipt-249" class="node default"><rect height="54" width="230.75" y="-27" x="-115.375" style="" class="basic label-container"></rect><g transform="translate(-85.375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="170.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Generate Claim Receipt</p></span></div></foreignObject></g></g><g transform="translate(575.0234375, 3394.15625)" id="flowchart-ReinsRecovery-251" class="node default reinsurance"><rect height="54" width="215.328125" y="-27" x="-107.6640625" style="fill:#f3e5f5 !important;stroke:#7b1fa2 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-77.6640625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="155.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Reinsurance Recovery</p></span></div></foreignObject></g></g><g transform="translate(1074.55078125, 3498.15625)" id="flowchart-ClaimSettled-253" class="node default important"><rect height="54" width="156.828125" y="-27" x="-78.4140625" style="fill:#fce4ec !important;stroke:#c2185b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-48.4140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Claim Settled</p></span></div></foreignObject></g></g><g transform="translate(1087.82421875, 2844.28125)" id="flowchart-Amendment-255" class="node default process"><rect height="54" width="191.984375" y="-27" x="-95.9921875" style="fill:#e3f2fd !important;stroke:#1565c0 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-65.9921875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="131.984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Policy Amendment</p></span></div></foreignObject></g></g><g transform="translate(1309.23828125, 2844.28125)" id="flowchart-Expiry-257" class="node default"><rect height="54" width="150.84375" y="-27" x="-75.421875" style="" class="basic label-container"></rect><g transform="translate(-45.421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="90.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Policy Expiry</p></span></div></foreignObject></g></g><g transform="translate(1017.94140625, 199.53125)" id="flowchart-Campaign-261" class="node default decision"><polygon style="fill:#fff3e0 !important;stroke:#ef6c00 !important;stroke-width:2px !important" transform="translate(-102.53125,102.53125)" class="label-container" points="102.53125,0 205.0625,-102.53125 102.53125,-205.0625 0,-102.53125"></polygon><g transform="translate(-75.53125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="151.0625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Marketing Campaign?</p></span></div></foreignObject></g></g><g transform="translate(949.39453125, 403.0625)" id="flowchart-CampaignLaunch-263" class="node default"><rect height="54" width="185.390625" y="-27" x="-92.6953125" style="" class="basic label-container"></rect><g transform="translate(-62.6953125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="125.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Launch Campaign</p></span></div></foreignObject></g></g><g transform="translate(1080.44140625, 3003.21875)" id="flowchart-End-273" class="node default startEnd"><rect height="39" width="50.984375" y="-19.5" x="-25.4921875" ry="19.5" rx="19.5" style="fill:#e8f5e8 !important;stroke:#2e7d32 !important;stroke-width:3px !important" class="basic label-container"></rect><g transform="translate(-13.1171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="26.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>End</p></span></div></foreignObject></g></g></g></g></g></svg>